:root {
    --graph-width: 97vw;
}

body {
    font-family: sans-serif;
}

#controls {
    display: flex;
}

.slidecontainer {
    width: var(--graph-width);
}

.slider {
    --sliderSize: 1%;
    -webkit-appearance: none;  /* Override default CSS styles */
    appearance: none;
    width: 100%; /* Full-width */
    height: 25px; /* Specified height */
    background: #d3d3d3; /* Grey background */
    /*outline: none; !* Remove outline *!*/
}

/* The slider handle (use -webkit- (Chrome, Opera, Safari, Edge) and -moz- (Firefox) to override default look) */
.slider::-webkit-slider-thumb {
    -webkit-appearance: none; /* Override default look */
    appearance: none;
    width: var(--sliderSize); /* Set a specific slider handle width */
    height: 25px; /* Slider handle height */
    background: #04AA6D; /* Green background */
    cursor: pointer; /* Cursor on hover */
}

#canvas-container {
    width: var(--graph-width);
    height: calc(100vh - 4rem);
    /*border: 1px solid red;*/
}

#reading-selection, #reading-selection-toggle {
    position: absolute;
    background-color: white;
    right: 1rem;
    border: 2px solid grey;
}

#message-log {
    border: 1px solid grey;
    width: 20rem;
    height: 6rem;
    overflow-y: scroll;
    overflow-x: clip;
}

#commands-page {
    display: grid;
    grid-template-columns: 50% auto;
}

.radio {
    cursor: pointer;
}

.radio img {
    width: 20px;
    height: 20px;
}