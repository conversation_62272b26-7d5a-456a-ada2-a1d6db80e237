<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Heatpump</title>
    <link rel="icon" href="data:;base64,iVBORw0KGgo=">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@40,300,0,0" />
    <link href="styles.css" rel="stylesheet">

    <script src="./libs/babel.min.js"></script>
    <script src="./libs/system.min.js"></script>
    <script type="systemjs-importmap">
        {
          "imports": {
            "react": "./libs/react.production.min.js",
            "react-dom": "./libs/react-dom.production.min.js",
            "react-router-dom": "./libs/react-router-dom.min.js"
          }
        }
    </script>

    <script>
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = { isDisabled: true };
    </script>

    <script type="module" src="code/systemjs-setup.js"></script>
</head>

<body data-bs-theme="dark" class="d-flex justify-content-center test-border">

<div class="container">

    <main id="main"></main>

</div>

</body>
</html>