import {sum, round} from "./common.js";

export function getTotalConsumption(readingsList) {
    const sumCb = (prev, current) => prev + current;

    const sum = readingsList
        .map(readings => readings.getConsumption())
        .reduce(sumCb, 0);

    return (sum / 60).toFixed(2);
}

export function getCopByTemp(readingsList) {
    const result = [];
    for (const readingsByTemp of getHourlyEnergyByTemp(readingsList)) {
        const [temp, readingsList] = readingsByTemp;
        const consumed = sum(readingsList.map(r => r.getConsumption()));
        const produced = sum(readingsList.map(r => r.getProduction()));
        result.push([temp, round(produced / consumed)]);
    }

    return result;
}

export function getHourlyEnergyByTemp(readingsList) {

    const readingsByTemp = new Map();

    readingsList = readingsList.filter(readings => readings.isRunning());

    for (const readings of readingsList) {
        const temp = readings.getOutsideTemp();
        if (!readingsByTemp.has(temp)) {
            readingsByTemp.set(temp, []);
        }

        const list = readingsByTemp.get(temp);

        list.push(readings);
    }

    const results = [];
    for (const key of readingsByTemp.keys()) {
        const readings = readingsByTemp.get(key);

        if (readings.length >= 60) { // at least hours worth of readings
            results.push([key, readings]);
        }
    }

    return results.sort((a, b) => a[0] - b[0]);
}

export function getHourlyConsumptionByTemp(readingsList) {
    const result = [];
    for (const readingsByTemp of getHourlyEnergyByTemp(readingsList)) {
        const [temp, readingsList] = readingsByTemp;
        const consumed = sum(readingsList.map(r => r.getConsumption()));
        result.push([temp, round(consumed / readingsList.length)]);
    }

    return result;
}

export function getAverageRuntime(readingsList) {
    if (readingsList.length === 0) {
        return [];
    }

    const switchedOn = (prev, curr) => prev !== curr && curr === true;
    const switchedOff = (prev, curr) => prev !== curr && curr === false;

    let previousReading = readingsList[0].isRunning();
    let minutes = 0;
    let isRunning = true;
    const counts = [];

    for (const readings of readingsList) {
        const currentReading = readings.isRunning();

        if (switchedOff(previousReading, currentReading)) {
            isRunning = false;
            minutes = 0;
        } else if (switchedOn(previousReading, currentReading)) {
            if (minutes) {
                counts.push(minutes);
            }
        }

        previousReading = currentReading;

        if (!isRunning) {
            minutes++;
        }
    }

    return [];
}