
export default class Radio {

    constructor(name, initialValue, onChangeCallback) {
        this.value = initialValue;
        this.name = name;
        this.onChangeCallback = onChangeCallback;
        this.options = document.querySelectorAll(`[data-name="${this.name}"]`);

        for (const each of this.options) {
            each.addEventListener('click', this.requestStateChange.bind(this));
        }

        this.paint();
    }

    setValue(newValue) {
        this.value = newValue;
        this.paint();
    }

    requestStateChange(event) {
        let selected = event.target;
        if (selected.tagName === 'IMG') {
            selected = selected.parentNode;
        }

        let newValue = selected.getAttribute('data-value');
        console.log("Set: ", newValue);

        this.onChangeCallback(this, newValue);
    }

    paint() {
        for (const each of this.options) {
            if (each.getAttribute('data-value') === this.value) {
                each.classList.add('selected');
                each.firstElementChild.src = 'img/radio-on.svg';
            } else {
                each.classList.remove('selected');
                each.firstElementChild.src = 'img/radio-off.svg';
            }
        }
    }
}
