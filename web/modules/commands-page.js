import applyTemplate from './template.js';
import {asNode} from './common.js';
import Repository from "./repository.js";
import Radio from "./Radio.js";

export default async function renderCommandsPage(contentDiv, dao) {
    await applyTemplate(contentDiv, 'tpl/commands-page.html');

    await refreshMessages(contentDiv, dao);

    await initTemperatureRadios(contentDiv, dao);

    const testButton = document.getElementById('test-button');
    const valveEnableButton = document.getElementById('valve-enable-button');
    const valveDisableButton = document.getElementById('valve-disable-button');

    testButton.addEventListener('click', async () => {
        const result = await dao.testCommand();
        await expectMessages(contentDiv, dao);
        console.log("result: ", result);
    });

    const setPowerCallback = async (radio, newValue) => {
        const result = await dao.setPower(newValue === "on" ? 1 : 0);
        await expectMessages(contentDiv, dao);
        console.log("result: ", result);
    }

    new Radio("power-radio", null, setPowerCallback);

    const enableValveCallback = async (radio, newValue) => {
        const result = await dao.enableValve(newValue === "enabled" ? 1 : 0);
        await expectMessages(contentDiv, dao);
        console.log("result: ", result);
    }

    new Radio("valve-radio", null, enableValveCallback);
}

async function initTemperatureRadios(contentDiv, dao) {
    const tempBlock = document.getElementById('temperature-block');

    const latestReadings = await dao.fetchLatestReadings();
    const readingsList = latestReadings ? [latestReadings] : [];
    const repository = new Repository().update(readingsList);
    const temp = repository.getSize() > 0
        ? repository.getAllReadings()[0].getRequestTemp()
        : undefined;

    for (const number of [-1, 0, 1, 2, 3, 4, 5]) {
        const checked = number === temp ? 'checked' : '';

        const radio = `<label>
            <input type="radio" name="temperature" ${checked} value="${number}">${number}
        </label>`;
        let node = asNode(radio);

        node.addEventListener('change', async (e) => {
            const temp = e.target.value;

            const result = await dao.setTemperature(temp);

            await expectMessages(contentDiv, dao);

            console.log("result: ", result);
        });

        tempBlock.appendChild(node);
    }
}

async function expectMessages(contentDiv, dao) {
    for (const delay of [2000, 1000, 1000]) {
        // setTimeout(async () => refreshMessages(contentDiv, dao), delay);
        setTimeout(async () => fetchControllerState(dao), delay);
    }
}

async function refreshMessages(contentDiv, dao) {
    const messagesDiv = document.getElementById('message-log');

    const messages = await dao.fetchMessageLog();

    messagesDiv.innerHTML = messages.join('<br>');
}

async function fetchControllerState(dao) {
    const state = await dao.fetchControllerState();

    console.log(state);
}

