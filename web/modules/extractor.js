
const readingToIndex = [
    ['from_buffer_pi', 0],
    ['outside_pi', 1],
    ['from_pump_pi', 2],
    ['relay', 3],
    ['Pump_Flow', 4],
    ['Main_Inlet_Temp', 5],
    ['Main_Outlet_Temp', 6],
    ['Main_Target_Temp', 7],
    ['Outside_Temp', 8],
    ['Heat_Energy_Production', 9],
    ['Heat_Energy_Consumption', 10],
    ['Z1_Water_Temp', 11],
    ['Buffer_Temp', 12],
    ['Defrosting_State', 13],
    ['Compressor_Freq', 14],
    ['Operations_Counter', 15],
    ['Z1_Heat_Request_Temp', 16],
    ['Z1_Water_Target_Temp', 17],
    ['Room_Thermostat_Temp', 18],
    ['Internal_Heater_State', 19],
    ['COP', null],
];

const readingToIndexMap = new Map(readingToIndex);

export function getAllIds() {
    return readingToIndex.map(each => each[0]);
}

export function getReading(readings, readingId) {
    // const direction = readingList[3] === "1" ? 21 : 20;

    if (readingId === 'COP') {
        const prod = getFloatReading(readings, 'Heat_Energy_Production');
        const consumption = getFloatReading(readings, 'Heat_Energy_Consumption');

        return consumption === 0 ? 0 : (prod / consumption).toFixed(2);
    }

    return getFloatReading(readings, readingId);
}

export function getFloatReading(readings, readingId) {
    const reading = parseFloat(readings[readingToIndexMap.get(readingId)]);

    if (readingId === 'Heat_Energy_Production'
        || readingId === 'Heat_Energy_Consumption') {
        return reading / 1000;
    }

    return reading;
}

export function getLabels(lines) {
    return lines.map(line => line.slice(9, 14));
}