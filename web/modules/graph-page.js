import applyTemplate from './template.js';
import {asNode, createNode} from './common.js';
import {ChartWrapper, createGraphData} from "./graph.js";
import {getSelectedReadingIds, saveSelectedReadingIds} from "./store.js";

const pageSize = 200;
let isReadingSelectionVisible = false;

export default async function renderGraphPage(contentDiv, repository) {
    await applyTemplate(contentDiv, 'tpl/graph-page.html');

    const chartCanvas = document.getElementById('chartCanvas');
    const slider = document.getElementById("slider");
    const readingSelection = document.getElementById("reading-selection");
    const readingSelectionToggle = document.getElementById("reading-selection-toggle");

    const updateReadingSelectionVisibility = () => {
        const displayValue = isReadingSelectionVisible ? 'block' : 'none';
        readingSelection.style.display = displayValue;
    }

    readingSelectionToggle.onclick = () => {
        isReadingSelectionVisible = !isReadingSelectionVisible;
        updateReadingSelectionVisibility();
    };

    updateReadingSelectionVisibility();

    const chart = new ChartWrapper(createGraphData([]), chartCanvas);

    const startPos = Math.max(0, repository.getSize() - pageSize);

    updateChart(chart, repository, startPos);

    initSlider(slider, startPos, repository.getSize(), pageSize, pageStart => {
        updateChart(chart, repository, pageStart);
    });

    createReadingSelection(readingSelection, repository.getAllIds());
}

function updateChart(chart, repository, pageStart) {
    const readings = repository.getAllReadings()
        .slice(pageStart, pageStart + pageSize);

    const data = createGraphData(readings);

    chart.update(data);
}

function initSlider(slider, startPos, maxLength, pageSize, onChange) {
    slider.style.setProperty('--sliderSize', pageSize / maxLength * 100 + '%');
    slider.max = maxLength - pageSize;
    slider.value = startPos;
    slider.oninput = event => {
        onChange(parseInt(event.target.value));
    };
}

function createReadingSelection(div, allReadingIds) {
    const selectedReadingIds = new Set(getSelectedReadingIds());

    const onChangeCb = e => {
        if (e.target.checked) {
            selectedReadingIds.add(e.target.value);
        } else {
            selectedReadingIds.delete(e.target.value);
        }

        saveSelectedReadingIds(Array.from(selectedReadingIds.values()));

        window.dispatchEvent(new Event('refresh'));
    };

    for (const readingId of allReadingIds) {
        const inputHtml = `<input type="checkbox" 
                   value="${readingId}">`;

        const inputNode = asNode(inputHtml);

        inputNode.onchange = onChangeCb;

        inputNode.checked = selectedReadingIds.has(readingId);

        const label = createNode('label', inputNode,
            asNode(readingId), asNode('<br>'));

        div.appendChild(label);
    }
}
