
export default class Dao {
    async fetchReadings(date) {
        return fetch('api.php?cmd=readings&date=' + date).then(r => r.json());
    }

    async fetchLatestReadings() {
        return fetch('api.php?cmd=fetch-latest-readings')
            .then(r => r.json());
    }

    async fetchLatestDates() {
        return await fetch('api.php?cmd=latest-dates').then(r => r.json());
    }

    async fetchMessageLog() {
        return await fetch('api.php?cmd=fetch-message-log').then(r => r.json());
    }

    async fetchControllerState() {
        return await fetch('api.php?cmd=fetch-ctrl-state').then(r => r.json());
    }

    async setTemperature(temp) {
        const url = `api.php?cmd=set-temperature&temp=${temp}`;
        return await fetch(url).then(r => r.json());
    }

    async testCommand() {
        return await fetch('api.php?cmd=test').then(r => r.json());
    }

    async enableValve(enable) {
        const url = 'api.php?cmd=valve-enable&enable=' + enable;

        return await fetch(url).then(r => r.json());
    }

    async setPower(state) {
        const url = 'api.php?cmd=set-power&state=' + state;

        return await fetch(url).then(r => r.json());
    }
}
