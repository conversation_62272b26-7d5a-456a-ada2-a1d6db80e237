import {getSelectedReadingIds} from "./store.js";

export class ChartWrapper {

    constructor(data, chartCanvas) {
        Chart.defaults.elements.line.borderWidth = 1;
        Chart.defaults.elements.line.tension = 0.2;
        Chart.defaults.elements.point.radius = 2;

        this.chart = new Chart(chartCanvas, {
            type: 'line',
            data: data
        });
    }

    update(data) {
        this.chart.data = data;
        this.chart.update('none');
    }
}

export function createGraphData(readings) {

    const selectedIds = getSelectedReadingIds();

    const colors = ['red', 'green', 'blue', 'black',
                    'BlueViolet', 'CadetBlue', 'Chocolate',
                    'DarkOliveGreen', 'DeepPink'];

    const map = new Map(selectedIds.map(readingId =>
        [readingId, getSeriesEntry(readingId, colors.shift())]));

    for (const readingId of selectedIds) {
        for (const reading of readings) {
            map.get(readingId).data.push(reading.getReading(readingId));
        }
    }

    const labels = readings.map(reading => reading.getLabel());

    return {
        labels: labels,
        datasets: Array.from(map.values())
    }
}

function getSeriesEntry(readingId, color) {
    return {
        label: readingId,
        data: [],
        borderColor: color
    }
}

