import renderGraphPage from './graph-page.js';
import renderDetailsPage from "./details-page.js";
import initNav from "./nav.js";
import Repository from "./repository.js";
import renderCommandsPage from "./commands-page.js";
import Dao from "./dao.js";

const contentDiv = document.getElementById('content');
const repository = new Repository();
const dao = new Dao();

window.addEventListener("refresh", refresh);

await initNav(repository, dao);

refresh();

async function refresh(event) {
    // console.log('refresh');

    const queryString = window.location.search;

    const urlParams = new URLSearchParams(queryString);

    await selectPage(urlParams.get('page') || 'graph');
}

async function selectPage(pageId) {

    if (pageId === 'graph') {
        return renderGraphPage(contentDiv, repository);
    } else if (pageId === 'details') {
        return renderDetailsPage(contentDiv, repository);
    } else if (pageId === 'commands') {
        return renderCommandsPage(contentDiv, dao);
    } else {
        throw new Error('unknown page');
    }
}
