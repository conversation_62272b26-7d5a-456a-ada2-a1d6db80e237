import {formatDate, round} from "./common.js";

const readingToIndex = [
    ['time', 0],
    ['ext_temp_1', 1],
    ['ext_temp_2', 2],
    ['ext_temp_3', 3],
    ['heatpump_state', 4],
    ['defrosting_state', 5],
    ['internal_heater_state', 6],
    ['valve_state', 7],
    ['pump_flow', 8],
    ['inlet_temp', 9],
    ['outlet_temp', 10],
    ['target_temp', 11],
    ['outside_temp', 12],
    ['zone_water_temp', 13],
    ['buffer_temp', 14],
    ['room_thermostat_temp', 15],
    ['compressor_freq', 16],
    ['heat_energy_consumption', 17],
    ['heat_energy_production', 18],
    ['heat_request_temp', 19],
    ['pump_power', 20],
    ['COP', null],
];

const readingToIndexMap = new Map(readingToIndex);

export default class Repository {

    update(listOfLists) {
        this.readings = listOfLists;
        return this;
    }

    getSize() {
        return this.readings.length;
    }

    getReadings(readingKey) {
        const index = readingToIndexMap.get(readingKey);

        return this.readings.map(readingList => readingList[index]);
    }

    getAllReadings() {
        return this.readings.map(readingList => new ReadingSet(readingList));
    }

    getAllIds() {
        return readingToIndex.map(each => each[0]).slice(1);
    }
}

export class ReadingSet {

    constructor(values) {
        this.values = values;
    }

    getReading(readingKey) {
        const index = readingToIndexMap.get(readingKey);

        const reading = this.values[index];

        if (readingKey === 'heat_energy_production'
            || readingKey === 'heat_energy_consumption') {
            return reading / 1000;
        }  else if (readingKey === 'COP') {
            const prod = this.getReading('heat_energy_production');
            const consumption = this.getReading('heat_energy_consumption');
            const compressor = this.getReading('compressor_freq');
            const defrosting = this.getReading('defrosting_state');

            return compressor === 0 || defrosting === 1
                ? 0
                : round(prod / consumption);
        }


        return reading;
    }

    getConsumption() {
        return this.getReading('heat_energy_consumption');
    }

    getProduction() {
        return this.getReading('heat_energy_production');
    }

    isRunning() {
        return parseInt(this.getReading('compressor_freq')) > 0;
    }

    getOutsideTemp() {
        return parseInt(this.getReading('outside_temp'));
    }

    getRequestTemp() {
        return parseInt(this.getReading('heat_request_temp'));
    }

    getLabel() {
        return formatDate(this.values[0], 'hm');
    }

}
