import applyTemplate from './template.js';
import {getDiv} from './common.js';
import {getHourlyConsumptionByTemp, getAverageRuntime,
        getTotalConsumption, getCopByTemp} from "./calculator.js";

export default async function renderDetailsPage(contentDiv, repository) {
    await applyTemplate(contentDiv, 'tpl/details-page.html');

    renderTotalConsumption(repository);

    renderHourlyConsumption(repository);

    renderCop(repository);

    renderAverageRuntime(repository);
}

function renderTotalConsumption(repository) {
    const span = document.getElementById('total-consumption');

    span.innerText = getTotalConsumption(repository.getAllReadings());
}

function renderHourlyConsumption(repository) {
    const span = document.getElementById('hourly-consumption-by-temp');

    const nodes = getHourlyConsumptionByTemp(repository.getAllReadings())
        .map(entry => getDiv(entry[0] + ': ' + entry[1] + '<br>'));

    nodes.forEach(node => span.appendChild(node));
}

function renderAverageRuntime(repository) {

    const a = getAverageRuntime(repository.getAllReadings());

    return;
    const span = document.getElementById('average-runtime-by-temp');

    const nodes = getAverageRuntime(repository.getAllReadings())
        .map(entry => getDiv(entry[0] + ': ' + entry[1] + '<br>'));

    nodes.forEach(node => span.appendChild(node));
}

function renderCop(repository) {
    const span = document.getElementById('cop-by-temp');

    const nodes = getCopByTemp(repository.getAllReadings())
        .map(entry => getDiv(entry[0] + ': ' + entry[1] + '<br>'));

    nodes.forEach(node => span.appendChild(node));
}