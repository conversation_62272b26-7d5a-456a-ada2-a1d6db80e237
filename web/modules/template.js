const map = new Map();

async function getTemplate(fileName) {

    if (map.has(fileName)) {
        return map.get(fileName);
    }

    const html = await fetch(fileName)
        .then(response => response.text());

    map.set(fileName, html);

    return html;
}

export default async function applyTemplate(div, fileName) {

    while (div.firstChild) {
        div.removeChild(div.firstChild);
    }

    div.innerHTML = await getTemplate(fileName);
}
