import { asNode } from './common.js';

const fileSelect = document.getElementById("fileSelect");

let repository;
let dao;

export default async function initNav(repo, aDao) {
    repository = repo;
    dao = aDao;

    interceptLinks(document.querySelectorAll('nav > a'));

    fileSelect.addEventListener('change', async e => {
        const date = e.target.value;

        repository.update(await dao.fetchReadings(date));

        window.dispatchEvent(new Event('refresh'));
    });

    const dates = await dao.fetchLatestDates();

    const latestDate = dates[0] ?? null;

    repository.update(await dao.fetchReadings(latestDate));

    createFileOptions(fileSelect, dates, latestDate);
}

function interceptLinks(links) {
    for (const link of links) {
        link.addEventListener('click', e => {
            e.preventDefault();

            const href = e.target.href;

            history.pushState(null, null, href);

            window.dispatchEvent(new Event('refresh'));
        });
    }
}

function createFileOptions(element, dates, currentDate) {

    for (const file of dates) {
        const selectedText = file === currentDate ? 'selected' : '';

        const option = asNode(`<option value="${file}" ${selectedText}>${file}</option>`);

        element.appendChild(option);
    }
}
