import React, { Component, ReactNode } from 'react';

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        this.setState({ error, errorInfo });
    }

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                return this.props.fallback;
            }

            return (
                <div className="error-boundary">
                    <div className="alert alert-danger">
                        <h4>Something went wrong</h4>
                        <p>An error occurred while rendering this component.</p>
                        {process.env.NODE_ENV === 'development' && this.state.error && (
                            <details style={{ marginTop: '10px' }}>
                                <summary>Error details (development only)</summary>
                                <pre style={{ 
                                    fontSize: '0.8em', 
                                    backgroundColor: '#f8f9fa', 
                                    padding: '10px', 
                                    borderRadius: '4px',
                                    overflow: 'auto'
                                }}>
                                    {this.state.error.toString()}
                                    {this.state.errorInfo?.componentStack}
                                </pre>
                            </details>
                        )}
                        <button 
                            className="btn btn-primary"
                            onClick={() => window.location.reload()}
                            style={{ marginTop: '10px' }}
                        >
                            Reload Page
                        </button>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
