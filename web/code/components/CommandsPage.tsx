import React, { useState, useEffect } from 'react';
import { useDao } from '../context/AppContext.tsx';
import Radio from './Radio.tsx';
import Repository from '../services/Repository.ts';

interface ControllerState {
    messages: string[];
    readings: any;
    'ctrl-readings': {
        debug: number;
        power: number;
        valve_enabled: number;
        valve_direction: number;
        duty_cycle_value: number;
    };
}

const CommandsPage: React.FC = () => {
    const dao = useDao();
    const [messages, setMessages] = useState<string[]>([]);
    const [currentTemperature, setCurrentTemperature] = useState<number>(0);
    const [powerState, setPowerState] = useState<string>('off');
    const [valveState, setValveState] = useState<string>('disabled');
    const [controllerState, setControllerState] = useState<ControllerState | null>(null);

    // Initialize component
    useEffect(() => {
        initializeData();

        // Set up periodic refresh of controller state every 5 seconds
        const interval = setInterval(() => {
            loadControllerState();
        }, 5000);

        return () => clearInterval(interval);
    }, []);

    const initializeData = async () => {
        await refreshMessages();
        await loadCurrentTemperature();
        await loadControllerState();
    };

    const loadCurrentTemperature = async () => {
        try {
            const latestReadings = await dao.fetchLatestReadings();
            if (latestReadings) {
                const repository = new Repository().update([latestReadings]);
                if (repository.getSize() > 0) {
                    const temp = repository.getAllReadings()[0].getRequestTemp();
                    setCurrentTemperature(temp);
                }
            }
        } catch (error) {
            console.error('Failed to load current temperature:', error);
        }
    };

    const loadControllerState = async () => {
        try {
            const state = await dao.fetchControllerState();
            setControllerState(state);

            // Update power and valve states based on actual controller state
            if (state['ctrl-readings']) {
                const ctrlReadings = state['ctrl-readings'];
                setPowerState(ctrlReadings.power === 1 ? 'on' : 'off');
                setValveState(ctrlReadings.valve_enabled === 1 ? 'enabled' : 'disabled');
            }
        } catch (error) {
            console.error('Failed to load controller state:', error);
        }
    };

    const refreshMessages = async () => {
        try {
            const messageList = await dao.fetchMessageLog();
            setMessages(messageList);
        } catch (error) {
            console.error('Failed to refresh messages:', error);
        }
    };

    const handleTemperatureChange = async (temp: number) => {
        try {
            const result = await dao.setTemperature(temp);
            setCurrentTemperature(temp);
            await expectMessages();
            console.log('Temperature set result:', result);
        } catch (error) {
            console.error('Failed to set temperature:', error);
        }
    };

    const handlePowerChange = async (state: string) => {
        try {
            const powerOn = state === 'on';
            const result = await dao.setPower(powerOn);
            // Don't immediately update state - wait for controller state refresh
            await expectMessages();
            console.log('Power set result:', result);
        } catch (error) {
            console.error('Failed to set power:', error);
        }
    };

    const handleValveChange = async (state: string) => {
        try {
            const valveEnabled = state === 'enabled';
            const result = await dao.enableValve(valveEnabled);
            // Don't immediately update state - wait for controller state refresh
            await expectMessages();
            console.log('Valve set result:', result);
        } catch (error) {
            console.error('Failed to set valve:', error);
        }
    };

    const handleTestCommand = async () => {
        try {
            const result = await dao.testCommand();
            await expectMessages();
            console.log('Test command result:', result);
        } catch (error) {
            console.error('Failed to execute test command:', error);
        }
    };

    const expectMessages = async () => {
        // Refresh messages and controller state after delays (similar to original implementation)
        const delays = [2000, 1000, 1000];
        for (const delay of delays) {
            setTimeout(async () => {
                await refreshMessages();
                await loadControllerState(); // This will update power/valve states
            }, delay);
        }
    };

    const temperatureOptions = [-1, 0, 1, 2, 3, 4, 5].map(temp => ({
        value: temp.toString(),
        label: temp.toString()
    }));

    const powerOptions = [
        { value: 'off', label: 'off' },
        { value: 'on', label: 'on' }
    ];

    const valveOptions = [
        { value: 'disabled', label: 'on' },
        { value: 'enabled', label: 'off' }
    ];

    return (
        <div id="commands-page" className="commands-page">
            <div className="command-section">
                <h4>Temperature</h4>
                <div className="temperature-controls">
                    {temperatureOptions.map(option => (
                        <label key={option.value} className="temperature-option">
                            <input
                                type="radio"
                                name="temperature"
                                value={option.value}
                                checked={currentTemperature.toString() === option.value}
                                onChange={() => handleTemperatureChange(parseInt(option.value))}
                            />
                            {option.label}
                        </label>
                    ))}
                </div>
            </div>

            <div className="command-section">
                <h4>Power</h4>
                <Radio
                    name="power-radio"
                    value={powerState}
                    options={powerOptions}
                    onChange={handlePowerChange}
                />
            </div>

            <div className="command-section">
                <h4>Valve</h4>
                <Radio
                    name="valve-radio"
                    value={valveState}
                    options={valveOptions}
                    onChange={handleValveChange}
                />
            </div>

            <div className="command-section">
                <h4>Test</h4>
                <button 
                    id="test-button"
                    className="btn btn-primary"
                    onClick={handleTestCommand}
                >
                    Test
                </button>
            </div>

            <div className="command-section">
                <h4>Messages</h4>
                <div id="message-log" className="message-log">
                    {messages.map((message, index) => (
                        <div key={index} dangerouslySetInnerHTML={{ __html: message }} />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CommandsPage;
