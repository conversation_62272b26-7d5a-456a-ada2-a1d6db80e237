export function sum(array: number[]): number {
    const sumCb = (prev: number, current: number) => prev + current;
    return array.reduce(sumCb, 0);
}

export function round(number: number, digits: number = 2): number {
    const multiplier = Math.pow(10, digits);
    return Math.round(number * multiplier) / multiplier;
}

export function createNode(type: string, ...children: Node[]): HTMLElement {
    const node = document.createElement(type);
    
    for (const child of children) {
        node.appendChild(child);
    }
    
    return node;
}

export function navigateTo(url: string, data?: any): void {
    history.pushState(null, null, url);
    
    const event = new PopStateEvent('popstate');
    (event as any).state = data;
    
    window.dispatchEvent(event);
}

export function asNode(html: string): ChildNode | null {
    return getDiv(html).firstChild;
}

export function getDiv(html: string): HTMLDivElement {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div;
}

export function formatDate(timestamp: number, format: 'md' | 'hm'): string {
    const offset = new Date().getTimezoneOffset() * 60;
    const date = new Date((timestamp - offset) * 1000);
    
    const iso = date.toISOString();
    
    // 2023-03-09T00:00:47.000Z
    
    if (format === 'md') {
        return iso.slice(5, 10);
    } else if (format === 'hm') {
        return iso.slice(11, 16);
    } else {
        throw new Error('unknown format: ' + format);
    }
}
