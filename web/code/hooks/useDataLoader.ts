import { useEffect } from 'react';
import { useAppContext } from '../context/AppContext.tsx';

export function useDataLoader() {
    const { state, dispatch } = useAppContext();
    const { dao, selectedDate } = state;

    // Initialize data on mount
    useEffect(() => {
        initializeData();
    }, []);

    // Load readings when selected date changes
    useEffect(() => {
        if (selectedDate !== null) {
            loadReadings(selectedDate);
        }
    }, [selectedDate]);

    const initializeData = async () => {
        try {
            console.log('Initializing data...');
            dispatch({ type: 'SET_LOADING', payload: true });

            // Fetch available dates
            console.log('Fetching latest dates...');
            const dates = await dao.fetchLatestDates();
            console.log('Received dates:', dates);
            dispatch({ type: 'SET_AVAILABLE_DATES', payload: dates });

            // Set the latest date as selected
            const latestDate = dates[0] ?? null;
            console.log('Selected date:', latestDate);
            dispatch({ type: 'SET_SELECTED_DATE', payload: latestDate });

            dispatch({ type: 'SET_ERROR', payload: null });
        } catch (error) {
            console.error('Failed to initialize data:', error);
            dispatch({ type: 'SET_ERROR', payload: 'Failed to load initial data' });
        } finally {
            dispatch({ type: 'SET_LOADING', payload: false });
        }
    };

    const loadReadings = async (date: string | null) => {
        try {
            console.log('Loading readings for date:', date);
            dispatch({ type: 'SET_LOADING', payload: true });

            const readings = await dao.fetchReadings(date);
            console.log('Received readings:', readings.length, 'items');
            dispatch({ type: 'UPDATE_REPOSITORY_DATA', payload: readings });

            dispatch({ type: 'SET_ERROR', payload: null });
        } catch (error) {
            console.error('Failed to load readings:', error);
            dispatch({ type: 'SET_ERROR', payload: 'Failed to load readings' });
        } finally {
            dispatch({ type: 'SET_LOADING', payload: false });
        }
    };

    const changeSelectedDate = (date: string | null) => {
        console.log('Changing selected date to:', date);
        dispatch({ type: 'SET_SELECTED_DATE', payload: date });
    };

    const refreshData = () => {
        if (selectedDate !== null) {
            loadReadings(selectedDate);
        } else {
            initializeData();
        }
    };

    return {
        changeSelectedDate,
        refreshData,
        isLoading: state.isLoading,
        error: state.error,
        availableDates: state.availableDates,
        selectedDate: state.selectedDate,
    };
}
