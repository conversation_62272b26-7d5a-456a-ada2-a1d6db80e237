import React from 'react';
import { Redirect, Route, Switch } from "react-router-dom";
import { useDataLoader } from './hooks/useDataLoader.ts';
import { useAppState, useRepository } from './context/AppContext.tsx';
import { useRefresh } from './hooks/useRefresh.ts';
import { isDevelopment } from './utils/common.ts';
import Navigation from './components/Navigation.tsx';
import GraphPage from './components/GraphPage.tsx';
import DetailsPage from './components/DetailsPage.tsx';
import CommandsPage from './components/CommandsPage.tsx';

const AppComp = () => {
    const { isLoading, error, availableDates, selectedDate } = useDataLoader();
    const repository = useRepository();

    // Initialize refresh event handling
    useRefresh();

    return (
        <div>
            <header>
                <Navigation />
                {error && (
                    <div className="alert alert-danger" role="alert">
                        Error: {error}
                    </div>
                )}
            </header>

            <div className="container">
                {/* Development status - can be removed in production */}
                {isDevelopment() && (
                    <div className="dev-status" style={{
                        padding: '15px',
                        backgroundColor: '#f8f9fa',
                        border: '1px solid #dee2e6',
                        borderRadius: '4px',
                        margin: '10px 0',
                        fontSize: '0.9em'
                    }}>
                        <h5>Phase 2 Status - Navigation & Components</h5>
                        <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px'}}>
                            <div>
                                <strong>Data:</strong> {repository.getSize()} readings
                            </div>
                            <div>
                                <strong>Dates:</strong> {availableDates.length} available
                            </div>
                            <div>
                                <strong>Selected:</strong> {selectedDate || 'None'}
                            </div>
                            <div>
                                <strong>Status:</strong> {isLoading ? 'Loading...' : 'Ready'}
                            </div>
                        </div>
                    </div>
                )}

                <Switch>
                    <Route path="/graph">
                        <GraphPage />
                    </Route>
                    <Route path="/details">
                        <DetailsPage />
                    </Route>
                    <Route path="/commands">
                        <CommandsPage />
                    </Route>
                    <Route path="/">
                        <Redirect to='/graph' />
                    </Route>
                </Switch>
            </div>
        </div>
    );
}

export default AppComp;