import React from 'react';
import { Redirect, Route, Switch } from "react-router-dom";
import { useDataLoader } from './hooks/useDataLoader.ts';
import { useAppState, useRepository } from './context/AppContext.tsx';

const AppComp = () => {
    const { isLoading, error, availableDates, selectedDate } = useDataLoader();
    const repository = useRepository();

    return (
        <div>
            <header>
                <nav className="navbar">
                    <div>Heatpump Application</div>
                    {isLoading && <div>Loading...</div>}
                    {error && <div style={{color: 'red'}}>Error: {error}</div>}
                </nav>
            </header>

            <div className="container">
                <div style={{padding: '20px', border: '1px solid #ccc', margin: '10px'}}>
                    <h3>Phase 1 Status - Core Infrastructure</h3>
                    <p><strong>Available dates:</strong> {availableDates.length} dates loaded</p>
                    <p><strong>Selected date:</strong> {selectedDate || 'None'}</p>
                    <p><strong>Repository size:</strong> {repository.getSize()} readings</p>
                    <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                    {error && <p style={{color: 'red'}}><strong>Error:</strong> {error}</p>}

                    {availableDates.length > 0 && (
                        <div>
                            <h4>Available Dates:</h4>
                            <select value={selectedDate || ''} onChange={(e) => {
                                // This will be implemented in Phase 2 with proper navigation
                                console.log('Date selected:', e.target.value);
                            }}>
                                {availableDates.map(date => (
                                    <option key={date} value={date}>{date}</option>
                                ))}
                            </select>
                        </div>
                    )}
                </div>

                <Switch>
                    <Route path="/graph">
                        <div>Graph Page - Coming Soon</div>
                    </Route>
                    <Route path="/details">
                        <div>Details Page - Coming Soon</div>
                    </Route>
                    <Route path="/commands">
                        <div>Commands Page - Coming Soon</div>
                    </Route>
                    <Route path="/">
                        <Redirect to='/graph' />
                    </Route>
                </Switch>
            </div>
        </div>
    );
}

export default AppComp;