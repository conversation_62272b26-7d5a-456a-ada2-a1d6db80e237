import React from 'react'
import ReactDOM from 'react-dom'

import AppComp from './AppComp.tsx';
import { HashRouter } from "react-router-dom";
import { AppProvider } from './context/AppContext.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';

ReactDOM.createRoot(document.getElementById("main")).render(
    <React.StrictMode>
        <AppProvider>
            <HashRouter basename='/'>
                <AppComp />
            </HashRouter>
        </AppProvider>
    </React.StrictMode>);
